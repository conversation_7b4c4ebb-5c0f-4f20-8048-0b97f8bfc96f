{"name": "universal-ai-brain-comprehensive-test", "version": "1.0.0", "type": "module", "description": "The most comprehensive test of Universal AI Brain with real-life customer support agent", "main": "index.js", "scripts": {"test:before": "tsx src/test-before-ai-brain.ts", "test:after": "tsx src/test-after-ai-brain.ts", "test:comprehensive": "tsx src/comprehensive-test.ts", "dev": "<PERSON>ra dev", "analyze:collections": "tsx src/analyze-collections.ts"}, "keywords": ["ai", "brain", "mongodb", "mastra", "customer-support"], "author": "Universal AI Brain Team", "license": "MIT", "dependencies": {"@ai-sdk/openai": "latest", "@mastra/core": "latest", "@mastra/memory": "latest", "@mongodb-ai/core": "file:../packages/core", "ai": "latest", "chalk": "^5.4.1", "dotenv": "^16.4.5", "mongodb": "^6.5.0", "ora": "^8.2.0", "uuid": "^9.0.1", "zod": "^3.25.56"}, "devDependencies": {"@types/node": "^20.11.24", "@types/uuid": "^9.0.8", "tsx": "^4.19.3", "typescript": "^5.3.3"}}