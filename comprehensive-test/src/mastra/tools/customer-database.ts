/**
 * Customer Database Tool - Simulates real customer database operations
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Simulated customer database
const customerDatabase = new Map([
  ['CUST001', {
    id: 'CUST001',
    name: '<PERSON>',
    email: '<EMAIL>',
    plan: 'Premium',
    joinDate: '2023-01-15',
    lastLogin: '2024-12-20',
    totalOrders: 15,
    totalSpent: 2450.00,
    status: 'active',
    preferences: {
      notifications: true,
      newsletter: true,
      language: 'en'
    },
    supportHistory: [
      { date: '2024-11-15', issue: 'Billing question', status: 'resolved' },
      { date: '2024-10-03', issue: 'Feature request', status: 'pending' }
    ]
  }],
  ['CUST002', {
    id: 'CUST002',
    name: '<PERSON>',
    email: '<EMAIL>',
    plan: 'Enterprise',
    joinDate: '2022-08-20',
    lastLogin: '2024-12-21',
    totalOrders: 45,
    totalSpent: 12750.00,
    status: 'active',
    preferences: {
      notifications: true,
      newsletter: false,
      language: 'en'
    },
    supportHistory: [
      { date: '2024-12-10', issue: 'API integration help', status: 'resolved' },
      { date: '2024-11-28', issue: 'Performance optimization', status: 'resolved' }
    ]
  }],
  ['CUST003', {
    id: 'CUST003',
    name: 'Emma Rodriguez',
    email: '<EMAIL>',
    plan: 'Basic',
    joinDate: '2024-06-10',
    lastLogin: '2024-12-19',
    totalOrders: 3,
    totalSpent: 150.00,
    status: 'active',
    preferences: {
      notifications: true,
      newsletter: true,
      language: 'es'
    },
    supportHistory: [
      { date: '2024-12-15', issue: 'Getting started help', status: 'resolved' }
    ]
  }]
]);

export const lookupCustomerTool = createTool({
  id: "lookup-customer",
  description: "Look up customer information by customer ID or email",
  inputSchema: z.object({
    identifier: z.string().describe("Customer ID or email address"),
    includeHistory: z.boolean().default(false).describe("Include support history")
  }),
  outputSchema: z.object({
    success: z.boolean(),
    customer: z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
      plan: z.string(),
      joinDate: z.string(),
      lastLogin: z.string(),
      totalOrders: z.number(),
      totalSpent: z.number(),
      status: z.string(),
      preferences: z.object({
        notifications: z.boolean(),
        newsletter: z.boolean(),
        language: z.string()
      }),
      supportHistory: z.array(z.object({
        date: z.string(),
        issue: z.string(),
        status: z.string()
      })).optional()
    }).optional(),
    message: z.string()
  }),
  execute: async ({ identifier, includeHistory }) => {
    // Simulate database lookup delay
    await new Promise(resolve => setTimeout(resolve, 200));

    // Look up by ID first
    let customer = customerDatabase.get(identifier);
    
    // If not found by ID, search by email
    if (!customer) {
      for (const [id, customerData] of customerDatabase) {
        if (customerData.email.toLowerCase() === identifier.toLowerCase()) {
          customer = customerData;
          break;
        }
      }
    }

    if (!customer) {
      return {
        success: false,
        message: `Customer not found with identifier: ${identifier}`
      };
    }

    const result = {
      success: true,
      customer: {
        ...customer,
        supportHistory: includeHistory ? customer.supportHistory : undefined
      },
      message: `Customer found: ${customer.name}`
    };

    return result;
  }
});

export const updateCustomerTool = createTool({
  id: "update-customer",
  description: "Update customer information or preferences",
  inputSchema: z.object({
    customerId: z.string().describe("Customer ID"),
    updates: z.object({
      preferences: z.object({
        notifications: z.boolean().optional(),
        newsletter: z.boolean().optional(),
        language: z.string().optional()
      }).optional(),
      status: z.string().optional()
    })
  }),
  outputSchema: z.object({
    success: z.boolean(),
    message: z.string(),
    updatedFields: z.array(z.string())
  }),
  execute: async ({ customerId, updates }) => {
    await new Promise(resolve => setTimeout(resolve, 150));

    const customer = customerDatabase.get(customerId);
    if (!customer) {
      return {
        success: false,
        message: `Customer not found: ${customerId}`,
        updatedFields: []
      };
    }

    const updatedFields: string[] = [];

    if (updates.preferences) {
      if (updates.preferences.notifications !== undefined) {
        customer.preferences.notifications = updates.preferences.notifications;
        updatedFields.push('notifications');
      }
      if (updates.preferences.newsletter !== undefined) {
        customer.preferences.newsletter = updates.preferences.newsletter;
        updatedFields.push('newsletter');
      }
      if (updates.preferences.language !== undefined) {
        customer.preferences.language = updates.preferences.language;
        updatedFields.push('language');
      }
    }

    if (updates.status) {
      customer.status = updates.status;
      updatedFields.push('status');
    }

    return {
      success: true,
      message: `Customer ${customer.name} updated successfully`,
      updatedFields
    };
  }
});

export const createSupportTicketTool = createTool({
  id: "create-support-ticket",
  description: "Create a new support ticket for a customer",
  inputSchema: z.object({
    customerId: z.string().describe("Customer ID"),
    issue: z.string().describe("Description of the issue"),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
    category: z.enum(['billing', 'technical', 'feature-request', 'bug-report', 'general']).default('general')
  }),
  outputSchema: z.object({
    success: z.boolean(),
    ticketId: z.string().optional(),
    message: z.string()
  }),
  execute: async ({ customerId, issue, priority, category }) => {
    await new Promise(resolve => setTimeout(resolve, 300));

    const customer = customerDatabase.get(customerId);
    if (!customer) {
      return {
        success: false,
        message: `Customer not found: ${customerId}`
      };
    }

    const ticketId = `TICKET-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // Add to customer's support history
    customer.supportHistory.push({
      date: new Date().toISOString().split('T')[0],
      issue: `${category}: ${issue}`,
      status: 'open'
    });

    return {
      success: true,
      ticketId,
      message: `Support ticket ${ticketId} created for ${customer.name}. Priority: ${priority}, Category: ${category}`
    };
  }
});

export const getKnowledgeBaseTool = createTool({
  id: "search-knowledge-base",
  description: "Search the knowledge base for solutions to common issues",
  inputSchema: z.object({
    query: z.string().describe("Search query for knowledge base"),
    category: z.enum(['billing', 'technical', 'account', 'features', 'troubleshooting']).optional()
  }),
  outputSchema: z.object({
    success: z.boolean(),
    articles: z.array(z.object({
      id: z.string(),
      title: z.string(),
      summary: z.string(),
      category: z.string(),
      relevanceScore: z.number()
    })),
    message: z.string()
  }),
  execute: async ({ query, category }) => {
    await new Promise(resolve => setTimeout(resolve, 250));

    // Simulated knowledge base articles
    const knowledgeBase = [
      {
        id: 'KB001',
        title: 'How to Reset Your Password',
        summary: 'Step-by-step guide to reset your account password using email verification.',
        category: 'account',
        keywords: ['password', 'reset', 'login', 'account', 'access']
      },
      {
        id: 'KB002',
        title: 'Understanding Your Billing Cycle',
        summary: 'Explanation of billing cycles, payment dates, and how to view your invoices.',
        category: 'billing',
        keywords: ['billing', 'payment', 'invoice', 'cycle', 'charge']
      },
      {
        id: 'KB003',
        title: 'API Rate Limits and Best Practices',
        summary: 'Information about API rate limits, how to optimize requests, and error handling.',
        category: 'technical',
        keywords: ['api', 'rate', 'limit', 'requests', 'optimization', 'error']
      },
      {
        id: 'KB004',
        title: 'Upgrading Your Plan',
        summary: 'How to upgrade your subscription plan and what features are included.',
        category: 'account',
        keywords: ['upgrade', 'plan', 'subscription', 'features', 'premium']
      },
      {
        id: 'KB005',
        title: 'Troubleshooting Connection Issues',
        summary: 'Common solutions for connectivity problems and network troubleshooting.',
        category: 'troubleshooting',
        keywords: ['connection', 'network', 'timeout', 'error', 'troubleshoot']
      }
    ];

    const queryLower = query.toLowerCase();
    const relevantArticles = knowledgeBase
      .map(article => {
        let relevanceScore = 0;

        // Check title match
        if (article.title.toLowerCase().includes(queryLower)) {
          relevanceScore += 0.5;
        }

        // Check keyword matches
        const matchingKeywords = article.keywords.filter(keyword =>
          queryLower.includes(keyword) || keyword.includes(queryLower)
        );
        relevanceScore += matchingKeywords.length * 0.2;

        // Category bonus
        if (category && article.category === category) {
          relevanceScore += 0.3;
        }

        return {
          ...article,
          relevanceScore: Math.min(relevanceScore, 1.0)
        };
      })
      .filter(article => article.relevanceScore > 0.1)
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 3)
      .map(({ keywords, ...article }) => article);

    return {
      success: true,
      articles: relevantArticles,
      message: `Found ${relevantArticles.length} relevant articles for: ${query}`
    };
  }
});
