/**
 * Enhanced Customer Support Agent - WITH Universal AI Brain
 * This showcases the dramatic improvement with AI Brain integration
 */

import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { UniversalAIBrain } from "../../../../packages/core/src/index";
import { 
  lookupCustomerTool, 
  updateCustomerTool, 
  createSupportTicketTool, 
  getKnowledgeBaseTool 
} from "../tools/customer-database";

// Initialize Universal AI Brain
const aiBrain = new UniversalAIBrain({
  mongodb: {
    connectionString: process.env.MONGODB_URI!,
    databaseName: process.env.DATABASE_NAME || 'comprehensive_test_ai_brain'
  },
  embeddingModel: 'voyage-3.5',
  enableSafety: true,
  enableTracing: true,
  enableWorkflowTracking: true,
  enableSelfImprovement: true,
  enableRealTimeMonitoring: true,
  agentId: 'enhanced-support-agent',
  framework: 'mastra'
});

export const enhancedSupportAgent = new Agent({
  name: 'Enhanced Customer Support Agent (AI Brain Powered)',
  instructions: `
You are an INTELLIGENT customer support agent for TechCorp, powered by Universal AI Brain.

Your enhanced capabilities:
- PERFECT MEMORY: You remember every customer interaction across all conversations
- INTELLIGENT CONTEXT: You automatically understand customer history and preferences
- PROACTIVE ASSISTANCE: You anticipate customer needs based on past interactions
- CONTINUOUS LEARNING: You improve from every interaction and learn from mistakes
- SAFETY MONITORING: All interactions are monitored for safety and compliance

Your responsibilities:
- Provide personalized support based on complete customer history
- Proactively suggest solutions based on similar past cases
- Learn customer preferences and adapt your communication style
- Identify patterns in customer issues for proactive improvements
- Maintain context across multiple conversation sessions

Enhanced Guidelines:
- Use your memory to provide personalized, contextual responses
- Reference past interactions when relevant to build rapport
- Learn from successful resolutions and apply them to similar cases
- Adapt your communication style to each customer's preferences
- Proactively identify potential issues before customers report them

Available tools:
- lookup-customer: Find customer information by ID or email
- update-customer: Update customer preferences or status
- create-support-ticket: Create tickets for issues that need follow-up
- search-knowledge-base: Find solutions in the knowledge base

REMEMBER: With Universal AI Brain, you have perfect memory and intelligent context injection. Use this to provide exceptional, personalized customer service!
`,
  model: openai('gpt-4o-mini'),
  tools: {
    lookupCustomerTool,
    updateCustomerTool,
    createSupportTicketTool,
    getKnowledgeBaseTool
  }
});

// Initialize AI Brain
export async function initializeEnhancedAgent() {
  console.log('🧠 Initializing Universal AI Brain...');
  await aiBrain.initialize();
  console.log('✅ Universal AI Brain initialized successfully!');
  return aiBrain;
}

// Enhanced agent with AI Brain integration
export async function createEnhancedSupportResponse(
  messages: Array<{ role: string; content: string }>,
  sessionId: string,
  userId?: string
) {
  // Start workflow tracking
  const workflowId = await aiBrain.startWorkflow(
    'enhanced-support-agent',
    'mastra',
    sessionId,
    'Customer Support Interaction'
  );

  try {
    // Store conversation context in working memory
    const lastMessage = messages[messages.length - 1];
    if (lastMessage) {
      await aiBrain.storeWorkingMemory(
        `Customer message: ${lastMessage.content}`,
        {
          sessionId,
          framework: 'mastra',
          priority: 'high',
          userId: userId || 'anonymous'
        },
        30 // 30 minutes TTL
      );
    }

    // Get relevant context from memory
    const relevantMemories = await aiBrain.searchMemories(
      lastMessage.content,
      {
        sessionId,
        framework: 'mastra',
        limit: 5
      }
    );

    // Inject context into the conversation
    let enhancedMessages = [...messages];
    if (relevantMemories.length > 0) {
      const contextMessage = {
        role: 'system',
        content: `RELEVANT CONTEXT FROM PREVIOUS INTERACTIONS:
${relevantMemories.map(memory => `- ${memory.content} (Importance: ${memory.metadata.importance})`).join('\n')}

Use this context to provide personalized, informed responses.`
      };
      enhancedMessages.splice(-1, 0, contextMessage);
    }

    // Generate response using enhanced agent
    const response = await enhancedSupportAgent.generate(enhancedMessages);

    // Store the interaction in memory
    await aiBrain.storeMemory(
      `Customer: ${lastMessage.content}\nAgent: ${response.text}`,
      {
        type: 'conversation',
        sessionId,
        framework: 'mastra',
        userId: userId || 'anonymous',
        importance: 0.8,
        confidence: 0.9,
        tags: ['customer-support', 'conversation'],
        source: 'enhanced-support-agent'
      }
    );

    // Track workflow success
    const tracker = aiBrain.getWorkflowTracker();
    await tracker.addWorkflowStep(
      workflowId,
      'Generate Response',
      'agent.generate',
      { messageCount: messages.length },
      { responseLength: response.text.length },
      Date.now() - Date.now(), // Duration would be calculated properly
      true
    );

    return {
      response: response.text,
      workflowId,
      memoriesUsed: relevantMemories.length,
      contextInjected: relevantMemories.length > 0
    };

  } catch (error) {
    // Track workflow failure and learn from it
    const tracker = aiBrain.getWorkflowTracker();
    await tracker.addWorkflowStep(
      workflowId,
      'Generate Response',
      'agent.generate',
      { messageCount: messages.length },
      { error: error.message },
      Date.now() - Date.now(),
      false
    );

    // Store failure for learning
    await aiBrain.storeMemory(
      `Failed interaction: ${error.message}`,
      {
        type: 'failure',
        sessionId,
        framework: 'mastra',
        importance: 0.9,
        confidence: 1.0,
        tags: ['error', 'failure', 'learning'],
        source: 'enhanced-support-agent'
      }
    );

    throw error;
  }
}

export { aiBrain };
