/**
 * @file emotional-intelligence-real-life.test.ts - Real-life test of EmotionalIntelligenceEngine
 * 
 * This test demonstrates the EmotionalIntelligenceEngine working with real Mastra agents,
 * showcasing MongoDB's time-series capabilities, TTL indexes, and emotional analytics
 * in a production-like scenario.
 */

import { UniversalAIBrain } from '@mongodb-ai/core';
import { Agent } from '@mastra/core';
import { MongoClient, Db } from 'mongodb';
import { EmotionalIntelligenceEngine } from '@mongodb-ai/core/src/intelligence/EmotionalIntelligenceEngine';
import { EmotionalStateCollection } from '@mongodb-ai/core/src/collections/EmotionalStateCollection';

describe('🎭 EmotionalIntelligenceEngine - Real-Life Integration Test', () => {
  let mongoClient: MongoClient;
  let db: Db;
  let aiBrain: UniversalAIBrain;
  let emotionalEngine: EmotionalIntelligenceEngine;
  let emotionalCollection: EmotionalStateCollection;
  let supportAgent: Agent;

  beforeAll(async () => {
    // Connect to MongoDB (using test database)
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    mongoClient = new MongoClient(mongoUri);
    await mongoClient.connect();
    db = mongoClient.db('emotional_intelligence_test');

    // Initialize Universal AI Brain with emotional intelligence
    aiBrain = new UniversalAIBrain({
      mongodb: {
        connectionString: mongoUri,
        databaseName: 'emotional_intelligence_test'
      },
      embeddingModel: 'voyage-3.5',
      enableSafety: true,
      enableTracing: true,
      enableWorkflowTracking: true,
      enableSelfImprovement: true,
      enableRealTimeMonitoring: true,
      agentId: 'emotional-test-agent',
      framework: 'mastra'
    });

    await aiBrain.initialize();

    // Get emotional intelligence components
    emotionalEngine = new EmotionalIntelligenceEngine(db);
    emotionalCollection = new EmotionalStateCollection(db);
    await emotionalEngine.initialize();

    // Create enhanced support agent
    supportAgent = new Agent({
      name: 'Emotionally Intelligent Support Agent',
      instructions: `
        You are an emotionally intelligent customer support agent powered by Universal AI Brain.
        You can detect and respond to customer emotions appropriately.
        
        Your emotional capabilities include:
        - Detecting customer emotional states from their messages
        - Adapting your response style based on emotions
        - Providing empathetic and contextually appropriate responses
        - Learning from emotional interactions to improve future responses
      `,
      model: {
        provider: 'OPEN_AI',
        name: 'gpt-4',
        toolChoice: 'auto'
      }
    });

    console.log('🎭 Real-life emotional intelligence test setup complete');
  });

  afterAll(async () => {
    // Cleanup
    await db.collection('agent_emotional_states').drop().catch(() => {});
    await mongoClient.close();
  });

  beforeEach(async () => {
    // Clean up emotional states before each test
    await db.collection('agent_emotional_states').deleteMany({});
  });

  describe('🌟 Real-Life Customer Support Scenarios', () => {
    it('should handle frustrated customer with emotional intelligence', async () => {
      console.log('\n🔥 Testing frustrated customer scenario...');
      
      const agentId = 'support-agent-001';
      const sessionId = 'frustrated-customer-session';
      
      // Simulate frustrated customer message
      const customerMessage = "I am absolutely furious! Your service has been terrible and I've been waiting for hours without any help!";
      
      // 1. Detect emotion from customer message
      const emotionalContext = {
        agentId,
        sessionId,
        input: customerMessage,
        conversationHistory: [
          { role: 'user', content: 'I need help with my account' },
          { role: 'assistant', content: 'I\'d be happy to help you with your account.' },
          { role: 'user', content: customerMessage }
        ],
        userContext: {
          urgency: 0.9,
          satisfaction: 0.1
        }
      };

      const detectedEmotion = await emotionalEngine.detectEmotion(emotionalContext);
      
      console.log('🎯 Detected emotion:', {
        primary: detectedEmotion.primary,
        intensity: detectedEmotion.intensity,
        valence: detectedEmotion.valence,
        confidence: detectedEmotion.confidence
      });

      // Verify emotion detection
      expect(detectedEmotion.primary).toBe('anger');
      expect(detectedEmotion.intensity).toBeGreaterThan(0.5);
      expect(detectedEmotion.valence).toBeLessThan(-0.3);
      expect(detectedEmotion.confidence).toBeGreaterThan(0.7);

      // 2. Process emotional state and get guidance
      const emotionalResponse = await emotionalEngine.processEmotionalState(
        emotionalContext,
        detectedEmotion,
        'Customer expressing frustration with service',
        'user_input'
      );

      console.log('🧠 Emotional guidance:', emotionalResponse.emotionalGuidance);
      console.log('💭 Cognitive impact:', emotionalResponse.cognitiveImpact);
      console.log('💡 Recommendations:', emotionalResponse.recommendations);

      // Verify emotional guidance for angry customer
      expect(emotionalResponse.emotionalGuidance.supportLevel).toBeGreaterThan(0.7);
      expect(emotionalResponse.emotionalGuidance.empathyLevel).toBeGreaterThan(0.5);
      expect(emotionalResponse.cognitiveImpact.attentionFocus).toContain('problem_solving');
      expect(emotionalResponse.recommendations).toContain('Focus on problem resolution and user support');

      // 3. Verify emotional state was stored in MongoDB with TTL
      const storedState = await emotionalCollection.getCurrentEmotionalState(agentId, sessionId);
      expect(storedState).toBeDefined();
      expect(storedState!.emotions.primary).toBe('anger');
      expect(storedState!.expiresAt).toBeDefined();
      expect(storedState!.context.trigger).toContain('frustration');

      console.log('✅ Frustrated customer scenario completed successfully');
    });

    it('should handle delighted customer with emotional intelligence', async () => {
      console.log('\n😊 Testing delighted customer scenario...');
      
      const agentId = 'support-agent-002';
      const sessionId = 'delighted-customer-session';
      
      // Simulate delighted customer message
      const customerMessage = "This is absolutely amazing! You've solved my problem perfectly and I'm so happy with the service!";
      
      const emotionalContext = {
        agentId,
        sessionId,
        input: customerMessage,
        conversationHistory: [
          { role: 'user', content: 'Can you help me with this issue?' },
          { role: 'assistant', content: 'I\'ve resolved the issue for you.' },
          { role: 'user', content: customerMessage }
        ],
        taskContext: {
          taskType: 'problem_resolution',
          progress: 1.0
        },
        userContext: {
          satisfaction: 0.95
        }
      };

      const detectedEmotion = await emotionalEngine.detectEmotion(emotionalContext);
      
      console.log('🎯 Detected emotion:', {
        primary: detectedEmotion.primary,
        intensity: detectedEmotion.intensity,
        valence: detectedEmotion.valence
      });

      // Verify positive emotion detection
      expect(detectedEmotion.primary).toBe('joy');
      expect(detectedEmotion.intensity).toBeGreaterThan(0.6);
      expect(detectedEmotion.valence).toBeGreaterThan(0.5);

      // Process emotional state
      const emotionalResponse = await emotionalEngine.processEmotionalState(
        emotionalContext,
        detectedEmotion,
        'Customer expressing delight with service',
        'user_input'
      );

      // Verify appropriate response for positive emotion
      expect(emotionalResponse.emotionalGuidance.responseStyle).toBe('confident');
      expect(emotionalResponse.cognitiveImpact.memoryPriority).toBeGreaterThan(0.7);
      
      console.log('✅ Delighted customer scenario completed successfully');
    });

    it('should handle worried customer with emotional intelligence', async () => {
      console.log('\n😰 Testing worried customer scenario...');
      
      const agentId = 'support-agent-003';
      const sessionId = 'worried-customer-session';
      
      const customerMessage = "I'm really worried about my data security. Are you sure my information is safe?";
      
      const emotionalContext = {
        agentId,
        sessionId,
        input: customerMessage,
        conversationHistory: [],
        userContext: {
          urgency: 0.7,
          satisfaction: 0.4
        }
      };

      const detectedEmotion = await emotionalEngine.detectEmotion(emotionalContext);
      
      console.log('🎯 Detected emotion:', {
        primary: detectedEmotion.primary,
        valence: detectedEmotion.valence,
        arousal: detectedEmotion.arousal
      });

      // Verify worry/fear detection
      expect(['fear', 'concern', 'worry']).toContain(detectedEmotion.primary);
      expect(detectedEmotion.valence).toBeLessThan(0);

      const emotionalResponse = await emotionalEngine.processEmotionalState(
        emotionalContext,
        detectedEmotion,
        'Customer expressing security concerns',
        'user_input'
      );

      // Verify appropriate guidance for worried customer
      expect(emotionalResponse.emotionalGuidance.supportLevel).toBeGreaterThan(0.6);
      expect(emotionalResponse.recommendations).toContain('Provide reassurance and build confidence');
      
      console.log('✅ Worried customer scenario completed successfully');
    });
  });

  describe('📊 MongoDB Time-Series and Analytics Features', () => {
    it('should demonstrate MongoDB time-series capabilities with emotional data', async () => {
      console.log('\n📈 Testing MongoDB time-series emotional analytics...');
      
      const agentId = 'analytics-agent';
      const sessionId = 'analytics-session';
      
      // Create a series of emotional interactions over time
      const emotionalInteractions = [
        { emotion: 'concern', valence: -0.3, trigger: 'Initial problem report', time: new Date(Date.now() - 3600000) },
        { emotion: 'frustration', valence: -0.6, trigger: 'Problem escalation', time: new Date(Date.now() - 1800000) },
        { emotion: 'relief', valence: 0.4, trigger: 'Problem acknowledgment', time: new Date(Date.now() - 900000) },
        { emotion: 'satisfaction', valence: 0.8, trigger: 'Problem resolution', time: new Date() }
      ];

      // Store emotional states with different timestamps
      for (const interaction of emotionalInteractions) {
        await emotionalCollection.recordEmotionalState({
          agentId,
          sessionId,
          timestamp: interaction.time,
          emotions: {
            primary: interaction.emotion,
            intensity: 0.7,
            valence: interaction.valence,
            arousal: 0.5,
            dominance: 0.5
          },
          context: {
            trigger: interaction.trigger,
            triggerType: 'user_input',
            conversationTurn: 1
          },
          cognitiveEffects: {
            attentionModification: 0.1,
            memoryStrength: 0.6,
            decisionBias: interaction.valence * 0.3,
            responseStyle: 'empathetic'
          },
          decay: {
            halfLife: 30,
            decayFunction: 'exponential',
            baselineReturn: 60
          },
          metadata: {
            framework: 'mastra',
            model: 'gpt-4',
            confidence: 0.85,
            source: 'detected',
            version: '1.0.0'
          }
        });
      }

      // Test MongoDB aggregation for emotional pattern analysis
      const patterns = await emotionalCollection.analyzeEmotionalPatterns(agentId, 1);
      
      console.log('📊 Emotional patterns analysis:', {
        dominantEmotions: patterns.dominantEmotions,
        emotionalStability: patterns.emotionalStability,
        triggerAnalysis: patterns.triggerAnalysis.length
      });

      // Verify pattern analysis
      expect(patterns.dominantEmotions).toBeInstanceOf(Array);
      expect(patterns.dominantEmotions.length).toBeGreaterThan(0);
      expect(patterns.triggerAnalysis).toBeInstanceOf(Array);
      expect(patterns.temporalPatterns).toBeInstanceOf(Array);

      // Test emotional timeline
      const timeline = await emotionalEngine.getEmotionalTimeline(agentId, sessionId, 4);
      
      console.log('📅 Emotional timeline:', {
        timelineLength: timeline.timeline.length,
        dominantEmotion: timeline.summary.dominantEmotion,
        avgValence: timeline.summary.avgValence,
        emotionalStability: timeline.summary.emotionalStability
      });

      expect(timeline.timeline.length).toBe(4);
      expect(timeline.summary.avgValence).toBeGreaterThan(-0.2); // Overall positive trend
      
      console.log('✅ MongoDB time-series analytics completed successfully');
    });

    it('should verify MongoDB indexes for optimal performance', async () => {
      console.log('\n🔍 Testing MongoDB index optimization...');
      
      // Verify that proper indexes exist for time-series queries
      const indexes = await db.collection('agent_emotional_states').listIndexes().toArray();
      const indexNames = indexes.map(idx => idx.name);
      
      console.log('📋 Available indexes:', indexNames);

      // Verify critical indexes for emotional intelligence
      expect(indexNames).toContain('agentId_timestamp_desc');
      expect(indexNames).toContain('emotional_decay_ttl');
      expect(indexNames).toContain('emotion_intensity_analysis');
      expect(indexNames).toContain('trigger_valence_analysis');
      
      // Verify TTL index configuration
      const ttlIndex = indexes.find(idx => idx.name === 'emotional_decay_ttl');
      expect(ttlIndex).toBeDefined();
      expect(ttlIndex!.expireAfterSeconds).toBe(0);
      
      console.log('✅ MongoDB index optimization verified');
    });
  });

  describe('🧠 Emotional Learning and Self-Improvement', () => {
    it('should demonstrate emotional learning capabilities', async () => {
      console.log('\n🎓 Testing emotional learning and improvement...');
      
      const agentId = 'learning-agent';
      
      // Create diverse emotional interactions for learning
      const learningScenarios = [
        { emotion: 'joy', trigger: 'successful_resolution', outcome: 'positive' },
        { emotion: 'anger', trigger: 'service_failure', outcome: 'negative' },
        { emotion: 'satisfaction', trigger: 'quick_response', outcome: 'positive' },
        { emotion: 'frustration', trigger: 'long_wait', outcome: 'negative' },
        { emotion: 'relief', trigger: 'problem_solved', outcome: 'positive' }
      ];

      for (const scenario of learningScenarios) {
        await emotionalCollection.recordEmotionalState({
          agentId,
          timestamp: new Date(),
          emotions: {
            primary: scenario.emotion,
            intensity: 0.7,
            valence: scenario.outcome === 'positive' ? 0.6 : -0.4,
            arousal: 0.5,
            dominance: 0.5
          },
          context: {
            trigger: scenario.trigger,
            triggerType: scenario.trigger.includes('failure') ? 'error' : 'task_completion',
            conversationTurn: 1
          },
          cognitiveEffects: {
            attentionModification: 0.2,
            memoryStrength: 0.8,
            decisionBias: 0.1,
            responseStyle: 'empathetic'
          },
          decay: {
            halfLife: 30,
            decayFunction: 'exponential',
            baselineReturn: 60
          },
          metadata: {
            framework: 'mastra',
            model: 'gpt-4',
            confidence: 0.8,
            source: 'detected',
            version: '1.0.0'
          }
        });
      }

      // Analyze emotional learning
      const learning = await emotionalEngine.analyzeEmotionalLearning(agentId, 1);
      
      console.log('🎓 Learning insights:', {
        patternsFound: learning.patterns.length,
        improvementsIdentified: learning.improvements.length,
        calibrationAccuracy: learning.calibration.accuracy
      });

      // Verify learning analysis
      expect(learning.patterns).toBeInstanceOf(Array);
      expect(learning.patterns.length).toBeGreaterThan(0);
      expect(learning.improvements).toBeInstanceOf(Array);
      expect(learning.calibration.accuracy).toBeGreaterThanOrEqual(0);
      expect(learning.calibration.accuracy).toBeLessThanOrEqual(1);

      // Verify pattern effectiveness analysis
      const positivePatterns = learning.patterns.filter(p => p.emotionalResponse === 'positive');
      const negativePatterns = learning.patterns.filter(p => p.emotionalResponse === 'negative');
      
      expect(positivePatterns.length).toBeGreaterThan(0);
      expect(negativePatterns.length).toBeGreaterThan(0);
      
      console.log('✅ Emotional learning analysis completed successfully');
    });
  });

  describe('📈 Performance and Statistics', () => {
    it('should provide comprehensive emotional intelligence statistics', async () => {
      console.log('\n📊 Testing emotional intelligence statistics...');
      
      const agentId = 'stats-agent';
      
      // Create sample emotional data
      await emotionalCollection.recordEmotionalState({
        agentId,
        timestamp: new Date(),
        emotions: {
          primary: 'joy',
          intensity: 0.8,
          valence: 0.9,
          arousal: 0.6,
          dominance: 0.7
        },
        context: {
          trigger: 'Customer satisfaction',
          triggerType: 'user_input',
          conversationTurn: 1
        },
        cognitiveEffects: {
          attentionModification: 0.3,
          memoryStrength: 0.9,
          decisionBias: 0.2,
          responseStyle: 'empathetic'
        },
        decay: {
          halfLife: 30,
          decayFunction: 'exponential',
          baselineReturn: 60
        },
        metadata: {
          framework: 'mastra',
          model: 'gpt-4',
          confidence: 0.85,
          source: 'detected',
          version: '1.0.0'
        }
      });

      // Get comprehensive statistics
      const stats = await emotionalEngine.getEmotionalStats(agentId);
      
      console.log('📈 Emotional intelligence statistics:', {
        totalStates: stats.totalStates,
        activeStates: stats.activeStates,
        avgIntensity: stats.avgIntensity,
        avgValence: stats.avgValence,
        dominantEmotions: stats.dominantEmotions.length
      });

      // Verify statistics
      expect(stats.totalStates).toBeGreaterThanOrEqual(1);
      expect(stats.activeStates).toBeGreaterThanOrEqual(0);
      expect(stats.avgIntensity).toBeGreaterThanOrEqual(0);
      expect(stats.avgValence).toBeGreaterThanOrEqual(-1);
      expect(stats.avgValence).toBeLessThanOrEqual(1);
      expect(stats.dominantEmotions).toBeInstanceOf(Array);
      
      console.log('✅ Emotional intelligence statistics verified');
    });
  });
});

console.log(`
🎭 EMOTIONAL INTELLIGENCE ENGINE - REAL-LIFE TEST SUMMARY
========================================================

This comprehensive test demonstrates the EmotionalIntelligenceEngine's capabilities:

✅ MONGODB ATLAS FEATURES SHOWCASED:
   • Time-series collections for emotional state tracking
   • TTL indexes for automatic emotional decay
   • Complex aggregation pipelines for pattern analysis
   • Optimized indexes for time-series queries
   • Real-time emotional state monitoring

✅ EMOTIONAL INTELLIGENCE CAPABILITIES:
   • Emotion detection from customer messages
   • Context-aware emotional response guidance
   • Emotional pattern analysis and learning
   • Cognitive impact assessment
   • Emotional timeline visualization

✅ REAL-LIFE SCENARIOS TESTED:
   • Frustrated customer support interactions
   • Delighted customer feedback processing
   • Worried customer reassurance handling
   • Multi-step emotional journey tracking

✅ PRODUCTION-READY FEATURES:
   • Comprehensive error handling
   • Performance optimization
   • Statistical analysis and reporting
   • Self-improvement and learning capabilities

The EmotionalIntelligenceEngine successfully demonstrates that MongoDB Atlas
is the ONLY database capable of handling the complex emotional data types
required for truly intelligent AI agents!
`);
