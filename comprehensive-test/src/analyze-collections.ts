/**
 * MongoDB Collections Analyzer
 * Analyzes all collections created by Universal AI Brain
 */

import 'dotenv/config';
import { MongoClient } from 'mongodb';
import chalk from 'chalk';
import ora from 'ora';

interface CollectionAnalysis {
  name: string;
  documentCount: number;
  averageDocumentSize: number;
  totalSize: number;
  indexes: any[];
  sampleDocuments: any[];
  purpose: string;
  keyFeatures: string[];
}

async function analyzeMongoDBCollections() {
  console.log(chalk.blue.bold('\n🔍 MONGODB COLLECTIONS ANALYSIS'));
  console.log(chalk.blue('=' .repeat(80)));
  console.log(chalk.yellow('Analyzing all collections created by Universal AI Brain\n'));

  const client = new MongoClient(process.env.MONGODB_URI!, {
    maxPoolSize: 5,
    minPoolSize: 1
  });

  try {
    await client.connect();
    const db = client.db(process.env.DATABASE_NAME || 'comprehensive_test_ai_brain');
    
    // Get all collections
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log(chalk.green(`📊 Found ${collections.length} collections in database`));
    console.log(chalk.gray(`   Collections: ${collectionNames.join(', ')}\n`));

    const analyses: CollectionAnalysis[] = [];

    // Analyze each collection
    for (const collectionInfo of collections) {
      const spinner = ora(`Analyzing ${collectionInfo.name}...`).start();
      
      try {
        const collection = db.collection(collectionInfo.name);
        
        // Get basic stats
        const documentCount = await collection.countDocuments();
        const stats = await db.command({ collStats: collectionInfo.name }).catch(() => null);
        
        // Get indexes
        const indexes = await collection.indexes();
        
        // Get sample documents
        const sampleDocuments = await collection.find({}).limit(3).toArray();
        
        // Calculate average document size
        const totalSize = stats?.size || 0;
        const averageDocumentSize = documentCount > 0 ? totalSize / documentCount : 0;
        
        // Determine purpose and features based on collection name and content
        const { purpose, keyFeatures } = analyzeCollectionPurpose(collectionInfo.name, sampleDocuments);
        
        const analysis: CollectionAnalysis = {
          name: collectionInfo.name,
          documentCount,
          averageDocumentSize: Math.round(averageDocumentSize),
          totalSize,
          indexes,
          sampleDocuments: sampleDocuments.map(doc => {
            // Remove large fields for display
            const { embedding, ...displayDoc } = doc;
            if (embedding) {
              displayDoc.embedding = `[Vector: ${embedding.length || 'N/A'} dimensions]`;
            }
            return displayDoc;
          }),
          purpose,
          keyFeatures
        };
        
        analyses.push(analysis);
        
        spinner.succeed(chalk.green(`✅ ${collectionInfo.name} - ${documentCount} documents`));
        
      } catch (error) {
        spinner.fail(chalk.red(`❌ ${collectionInfo.name} - Error: ${error.message}`));
      }
    }

    // Generate detailed report
    await generateCollectionReport(analyses);
    
    return analyses;

  } finally {
    await client.close();
  }
}

function analyzeCollectionPurpose(name: string, sampleDocs: any[]): { purpose: string; keyFeatures: string[] } {
  const purposes: Record<string, { purpose: string; keyFeatures: string[] }> = {
    'agent_memory': {
      purpose: 'Stores all conversation memories with semantic embeddings for intelligent retrieval',
      keyFeatures: [
        'Semantic vector embeddings',
        'Memory importance scoring',
        'Cross-conversation persistence',
        'Automatic decay algorithms',
        'Relationship mapping'
      ]
    },
    'agent_context': {
      purpose: 'Manages contextual information for intelligent response generation',
      keyFeatures: [
        'Session-based context',
        'Working memory with TTL',
        'Priority-based ranking',
        'Multi-source context merging',
        'Framework-specific optimization'
      ]
    },
    'agent_traces': {
      purpose: 'Comprehensive tracing of all agent interactions for debugging and optimization',
      keyFeatures: [
        'Complete interaction logging',
        'Performance metrics tracking',
        'Error analysis and debugging',
        'Tool usage analytics',
        'Response quality monitoring'
      ]
    },
    'agent_metrics': {
      purpose: 'Real-time performance analytics and system health monitoring',
      keyFeatures: [
        'Response time tracking',
        'Success/failure rates',
        'Cost monitoring',
        'Usage analytics',
        'Trend analysis'
      ]
    },
    'agent_workflows': {
      purpose: 'Multi-step process tracking and workflow optimization',
      keyFeatures: [
        'Step-by-step execution tracking',
        'Success/failure analysis',
        'Dependency management',
        'Retry mechanisms',
        'Performance optimization'
      ]
    },
    'agent_safety_logs': {
      purpose: 'Enterprise-grade safety monitoring and compliance logging',
      keyFeatures: [
        'PII detection and masking',
        'Hallucination prevention',
        'Content filtering',
        'Compliance audit trails',
        'Real-time safety alerts'
      ]
    }
  };

  // Check for exact match first
  if (purposes[name]) {
    return purposes[name];
  }

  // Try to infer from sample documents
  if (sampleDocs.length > 0) {
    const firstDoc = sampleDocs[0];
    
    if (firstDoc.embedding || firstDoc.vector) {
      return {
        purpose: 'Vector storage for semantic search and similarity matching',
        keyFeatures: ['Vector embeddings', 'Semantic search', 'Similarity matching']
      };
    }
    
    if (firstDoc.workflowId || firstDoc.steps) {
      return {
        purpose: 'Workflow and process tracking',
        keyFeatures: ['Process tracking', 'Step management', 'Execution monitoring']
      };
    }
    
    if (firstDoc.metrics || firstDoc.performance) {
      return {
        purpose: 'Performance and analytics data',
        keyFeatures: ['Metrics collection', 'Performance tracking', 'Analytics']
      };
    }
  }

  return {
    purpose: 'General data storage for AI Brain functionality',
    keyFeatures: ['Data persistence', 'Information storage']
  };
}

async function generateCollectionReport(analyses: CollectionAnalysis[]) {
  console.log(chalk.blue.bold('\n📋 DETAILED COLLECTION ANALYSIS'));
  console.log(chalk.blue('=' .repeat(80)));

  let totalDocuments = 0;
  let totalSize = 0;

  for (const analysis of analyses) {
    totalDocuments += analysis.documentCount;
    totalSize += analysis.totalSize;

    console.log(chalk.cyan.bold(`\n📁 ${analysis.name.toUpperCase()}`));
    console.log(chalk.cyan('-' .repeat(50)));
    
    console.log(chalk.white(`🎯 Purpose: ${analysis.purpose}`));
    
    console.log(chalk.white(`📊 Statistics:`));
    console.log(chalk.white(`   • Documents: ${analysis.documentCount.toLocaleString()}`));
    console.log(chalk.white(`   • Average Size: ${analysis.averageDocumentSize} bytes`));
    console.log(chalk.white(`   • Total Size: ${(analysis.totalSize / 1024).toFixed(2)} KB`));
    console.log(chalk.white(`   • Indexes: ${analysis.indexes.length}`));
    
    console.log(chalk.green(`⚡ Key Features:`));
    analysis.keyFeatures.forEach(feature => {
      console.log(chalk.green(`   • ${feature}`));
    });
    
    if (analysis.indexes.length > 0) {
      console.log(chalk.yellow(`🔍 Indexes:`));
      analysis.indexes.forEach(index => {
        const indexName = index.name === '_id_' ? 'Primary Key' : index.name;
        console.log(chalk.yellow(`   • ${indexName}`));
      });
    }
    
    if (analysis.sampleDocuments.length > 0) {
      console.log(chalk.gray(`📄 Sample Document Structure:`));
      const sampleDoc = analysis.sampleDocuments[0];
      const keys = Object.keys(sampleDoc).slice(0, 8); // Show first 8 fields
      keys.forEach(key => {
        const value = sampleDoc[key];
        const valueType = Array.isArray(value) ? 'Array' : typeof value;
        const valuePreview = typeof value === 'string' && value.length > 30 
          ? `${value.substring(0, 30)}...` 
          : value;
        console.log(chalk.gray(`   • ${key}: ${valueType} = ${JSON.stringify(valuePreview)}`));
      });
      if (Object.keys(sampleDoc).length > 8) {
        console.log(chalk.gray(`   • ... and ${Object.keys(sampleDoc).length - 8} more fields`));
      }
    }
  }

  // Summary
  console.log(chalk.blue.bold('\n📈 OVERALL SUMMARY'));
  console.log(chalk.blue('-' .repeat(50)));
  console.log(chalk.white(`📊 Total Collections: ${analyses.length}`));
  console.log(chalk.white(`📄 Total Documents: ${totalDocuments.toLocaleString()}`));
  console.log(chalk.white(`💾 Total Storage: ${(totalSize / 1024 / 1024).toFixed(2)} MB`));
  
  console.log(chalk.green.bold('\n🧠 AI BRAIN INTELLIGENCE INFRASTRUCTURE:'));
  console.log(chalk.green('✅ Memory System - Perfect recall with semantic search'));
  console.log(chalk.green('✅ Context Engine - Intelligent response enhancement'));
  console.log(chalk.green('✅ Workflow Tracking - Multi-step process optimization'));
  console.log(chalk.green('✅ Performance Analytics - Real-time monitoring'));
  console.log(chalk.green('✅ Safety Systems - Enterprise-grade protection'));
  console.log(chalk.green('✅ Tracing & Debugging - Complete interaction visibility'));
  
  console.log(chalk.magenta.bold('\n🎯 PRODUCTION READINESS:'));
  console.log(chalk.magenta('🚀 Scalable MongoDB Atlas infrastructure'));
  console.log(chalk.magenta('🔍 Optimized indexes for fast queries'));
  console.log(chalk.magenta('📊 Comprehensive analytics and monitoring'));
  console.log(chalk.magenta('🛡️ Enterprise-grade safety and compliance'));
  console.log(chalk.magenta('🧠 Intelligent memory and context management'));
}

// Run the analysis
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeMongoDBCollections()
    .then(analyses => {
      console.log(chalk.green.bold('\n🎉 COLLECTION ANALYSIS COMPLETE!'));
      console.log(chalk.green(`Analyzed ${analyses.length} collections successfully.\n`));
      process.exit(0);
    })
    .catch(error => {
      console.error(chalk.red.bold('\n💥 ANALYSIS FAILED:'), error);
      process.exit(1);
    });
}

export { analyzeMongoDBCollections };
