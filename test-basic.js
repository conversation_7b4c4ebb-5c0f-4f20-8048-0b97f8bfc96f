/**
 * Basic Test Script for Universal AI Brain - MongoDB Only
 * Tests core MongoDB functionality without requiring OpenAI
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

async function testBasicFunctionality() {
  console.log('🧪 Starting Basic Universal AI Brain Tests...\n');

  const mongoUri = process.env.MONGODB_URI;
  const databaseName = process.env.DATABASE_NAME || 'universal_ai_brain';

  let client;
  let testsPassed = 0;
  let testsTotal = 0;

  try {
    // Test 1: MongoDB Atlas Connection
    testsTotal++;
    console.log('📊 Test 1: MongoDB Atlas Connection');
    
    client = new MongoClient(mongoUri, {
      maxPoolSize: 5,
      minPoolSize: 1,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      retryReads: true
    });

    await client.connect();
    const db = client.db(databaseName);
    
    const result = await db.admin().ping();
    if (result.ok === 1) {
      console.log('✅ MongoDB Atlas connection successful');
      testsPassed++;
    } else {
      console.log('❌ MongoDB Atlas ping failed');
    }

    // Test 2: Create AI Brain Collections
    testsTotal++;
    console.log('\n📊 Test 2: AI Brain Collections Setup');
    
    const collections = [
      'agent_memory',
      'agent_context', 
      'agent_traces',
      'agent_metrics',
      'agent_workflows'
    ];

    for (const collectionName of collections) {
      await db.createCollection(collectionName);
    }
    
    const createdCollections = await db.listCollections().toArray();
    const collectionNames = createdCollections.map(c => c.name);
    
    const allCollectionsExist = collections.every(name => collectionNames.includes(name));
    
    if (allCollectionsExist) {
      console.log('✅ AI Brain collections created successfully');
      testsPassed++;
    } else {
      console.log('❌ AI Brain collections creation failed');
    }

    // Test 3: Memory Storage Operations
    testsTotal++;
    console.log('\n📊 Test 3: Memory Storage Operations');
    
    const memoryCollection = db.collection('agent_memory');
    
    // Store a memory
    const memory = {
      id: `memory_${Date.now()}`,
      content: 'Universal AI Brain test memory',
      metadata: {
        type: 'conversation',
        importance: 0.8,
        confidence: 0.9,
        source: 'test_agent',
        framework: 'universal',
        sessionId: `session_${Date.now()}`,
        tags: ['test', 'memory', 'ai_brain'],
        created: new Date(),
        updated: new Date()
      }
    };
    
    const insertResult = await memoryCollection.insertOne(memory);
    
    // Retrieve the memory
    const retrievedMemory = await memoryCollection.findOne({ id: memory.id });
    
    // Update the memory
    const updateResult = await memoryCollection.updateOne(
      { id: memory.id },
      { $set: { 'metadata.lastAccessed': new Date() } }
    );
    
    if (insertResult.insertedId && retrievedMemory && updateResult.modifiedCount === 1) {
      console.log('✅ Memory storage operations successful');
      testsPassed++;
    } else {
      console.log('❌ Memory storage operations failed');
    }

    // Test 4: Context Management
    testsTotal++;
    console.log('\n📊 Test 4: Context Management');
    
    const contextCollection = db.collection('agent_context');
    
    const context = {
      contextId: `context_${Date.now()}`,
      content: 'Universal AI Brain context data',
      source: 'test_agent',
      relevanceScore: 0.85,
      metadata: {
        type: 'conversation_context',
        framework: 'universal',
        sessionId: `session_${Date.now()}`,
        userId: 'test_user',
        tags: ['test', 'context'],
        importance: 0.8,
        confidence: 0.9,
        lastUsed: new Date(),
        usageCount: 1
      }
    };
    
    const contextInsert = await contextCollection.insertOne(context);
    const contextRetrieve = await contextCollection.findOne({ contextId: context.contextId });
    
    if (contextInsert.insertedId && contextRetrieve) {
      console.log('✅ Context management successful');
      testsPassed++;
    } else {
      console.log('❌ Context management failed');
    }

    // Test 5: Workflow Tracking
    testsTotal++;
    console.log('\n📊 Test 5: Workflow Tracking');
    
    const workflowCollection = db.collection('agent_workflows');
    
    const workflow = {
      workflowId: `workflow_${Date.now()}`,
      name: 'Test AI Brain Workflow',
      status: 'active',
      steps: [
        { id: 'step1', name: 'Initialize', status: 'completed' },
        { id: 'step2', name: 'Process', status: 'in_progress' },
        { id: 'step3', name: 'Finalize', status: 'pending' }
      ],
      metadata: {
        framework: 'universal',
        agentId: 'test_agent',
        sessionId: `session_${Date.now()}`,
        created: new Date(),
        updated: new Date()
      }
    };
    
    const workflowInsert = await workflowCollection.insertOne(workflow);
    const workflowRetrieve = await workflowCollection.findOne({ workflowId: workflow.workflowId });
    
    if (workflowInsert.insertedId && workflowRetrieve) {
      console.log('✅ Workflow tracking successful');
      testsPassed++;
    } else {
      console.log('❌ Workflow tracking failed');
    }

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    for (const collectionName of collections) {
      await db.collection(collectionName).deleteMany({});
      await db.collection(collectionName).drop();
    }

  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }

  // Close MongoDB connection
  if (client) {
    await client.close();
    console.log('\n🔌 Disconnected from MongoDB Atlas');
  }

  // Final Results
  console.log('\n' + '='.repeat(60));
  console.log('🎯 BASIC AI BRAIN TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${testsPassed}/${testsTotal}`);
  console.log(`📊 Success Rate: ${Math.round((testsPassed / testsTotal) * 100)}%`);
  
  if (testsPassed === testsTotal) {
    console.log('\n🎉 ALL BASIC TESTS PASSED! 🚀');
    console.log('✅ MongoDB Atlas: Ready');
    console.log('✅ Core Collections: Ready');
    console.log('✅ Memory Storage: Ready');
    console.log('✅ Context Management: Ready');
    console.log('✅ Workflow Tracking: Ready');
    console.log('\n🧠 Universal AI Brain Core Infrastructure: READY!');
  } else {
    console.log('\n⚠️ Some tests failed - check configuration');
  }
  
  return testsPassed === testsTotal;
}

// Run the tests
testBasicFunctionality()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
